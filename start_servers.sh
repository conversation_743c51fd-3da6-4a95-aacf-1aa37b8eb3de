#!/bin/bash
# AI Studio Bridge 一键启动脚本 (Linux/macOS)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
HISTORY_SERVER_PID=""
OPENAI_SERVER_PID=""

# 打印横幅
print_banner() {
    echo "========================================"
    echo "   🚀 AI Studio Bridge 一键启动器"
    echo "========================================"
    echo
}

# 打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message $RED "❌ 错误: 未找到 $1 命令"
        return 1
    fi
    return 0
}

# 检查Python版本
check_python() {
    if ! check_command python3; then
        print_message $RED "请安装 Python 3.8 或更高版本"
        return 1
    fi
    
    local python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    local major=$(echo $python_version | cut -d. -f1)
    local minor=$(echo $python_version | cut -d. -f2)
    
    if [ $major -lt 3 ] || ([ $major -eq 3 ] && [ $minor -lt 8 ]); then
        print_message $RED "❌ Python版本过低: $python_version (需要 >= 3.8)"
        return 1
    fi
    
    print_message $GREEN "✅ Python版本检查通过: $python_version"
    return 0
}

# 检查必要文件
check_files() {
    local files=("local_history_server.py" "openai_compatible_server.py" "requirements.txt")
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            print_message $RED "❌ 错误: 未找到 $file"
            return 1
        fi
    done
    
    print_message $GREEN "✅ 必要文件检查通过"
    return 0
}

# 安装依赖
install_dependencies() {
    print_message $BLUE "📦 检查并安装Python依赖..."
    
    if ! python3 -m pip install -r requirements.txt > /dev/null 2>&1; then
        print_message $RED "❌ 依赖安装失败"
        print_message $YELLOW "请手动运行: python3 -m pip install -r requirements.txt"
        return 1
    fi
    
    print_message $GREEN "✅ 依赖安装完成"
    return 0
}

# 检查端口是否可用
check_port() {
    local port=$1
    local timeout=${2:-5}
    
    if curl -s --connect-timeout $timeout "http://127.0.0.1:$port" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 启动服务器
start_server() {
    local script=$1
    local port=$2
    local name=$3
    local pid_var=$4
    
    print_message $BLUE "🚀 启动$name (端口 $port)..."
    
    # 启动服务器并获取PID
    python3 "$script" > /dev/null 2>&1 &
    local pid=$!
    
    # 将PID赋值给指定变量
    eval "$pid_var=$pid"
    
    # 等待服务器启动
    print_message $YELLOW "⏳ 等待${name}启动..."
    local count=0
    while [ $count -lt 10 ]; do
        sleep 1
        if check_port $port; then
            print_message $GREEN "✅ ${name}启动成功 (PID: $pid)"
            return 0
        fi
        count=$((count + 1))
    done
    
    print_message $RED "❌ ${name}启动失败或超时"
    return 1
}

# 停止服务器
stop_servers() {
    print_message $YELLOW "\n🛑 正在停止所有服务器..."
    
    if [ ! -z "$OPENAI_SERVER_PID" ]; then
        kill $OPENAI_SERVER_PID 2>/dev/null
        print_message $GREEN "✅ OpenAI兼容服务器已停止"
    fi
    
    if [ ! -z "$HISTORY_SERVER_PID" ]; then
        kill $HISTORY_SERVER_PID 2>/dev/null
        print_message $GREEN "✅ 本地历史服务器已停止"
    fi
    
    # 清理可能的残留进程
    pkill -f "local_history_server.py" 2>/dev/null
    pkill -f "openai_compatible_server.py" 2>/dev/null
}

# 信号处理函数
cleanup() {
    echo
    print_message $YELLOW "收到中断信号，正在停止服务器..."
    stop_servers
    exit 0
}

# 显示状态
show_status() {
    echo
    echo "========================================"
    print_message $GREEN "   🎉 所有服务器启动成功！"
    echo "========================================"
    echo
    echo "📍 服务器地址:"
    echo "   • 本地历史服务器: http://127.0.0.1:5101"
    echo "   • OpenAI兼容服务器: http://127.0.0.1:5100"
    echo
    echo "📝 下一步操作:"
    echo "   1. 确保浏览器已安装Tampermonkey扩展"
    echo "   2. 安装TampermonkeyScript文件夹中的三个脚本:"
    echo "      - automator.js"
    echo "      - historyforger.js"
    echo "      - modelfetcher.js"
    echo "   3. 打开AI Studio Chat历史对话页面(确保不是空对话页面)"
    echo
    print_message $YELLOW "⚠️  注意: 按 Ctrl+C 停止所有服务器"
    echo "========================================"
}

# 主函数
main() {
    # 设置信号处理
    trap cleanup SIGINT SIGTERM
    
    print_banner
    
    # 环境检查
    if ! check_python; then
        exit 1
    fi
    
    if ! check_files; then
        exit 1
    fi
    
    if ! install_dependencies; then
        exit 1
    fi
    
    echo
    print_message $BLUE "🔧 正在启动服务器..."
    echo
    
    # 启动本地历史服务器
    if ! start_server "local_history_server.py" 5101 "本地历史服务器" "HISTORY_SERVER_PID"; then
        exit 1
    fi
    
    # 启动OpenAI兼容服务器
    if ! start_server "openai_compatible_server.py" 5100 "OpenAI兼容服务器" "OPENAI_SERVER_PID"; then
        stop_servers
        exit 1
    fi
    
    # 显示状态
    show_status
    
    # 保持脚本运行
    print_message $BLUE "\n按 Ctrl+C 停止服务器..."
    while true; do
        sleep 1
        
        # 检查进程是否还在运行
        if ! kill -0 $HISTORY_SERVER_PID 2>/dev/null; then
            print_message $RED "⚠️  本地历史服务器意外停止"
            break
        fi
        
        if ! kill -0 $OPENAI_SERVER_PID 2>/dev/null; then
            print_message $RED "⚠️  OpenAI兼容服务器意外停止"
            break
        fi
    done
    
    stop_servers
}

# 检查是否以root权限运行
if [ "$EUID" -eq 0 ]; then
    print_message $YELLOW "⚠️  警告: 不建议以root权限运行此脚本"
fi

# 运行主函数
main "$@"
