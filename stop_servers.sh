#!/bin/bash
# AI Studio Bridge 服务器停止脚本 (Linux/macOS)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_banner() {
    echo "========================================"
    echo "   🛑 AI Studio Bridge 服务器停止器"
    echo "========================================"
    echo
}

# 检查端口占用
check_port_usage() {
    local port=$1
    local process=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$process" ]; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 停止指定端口的进程
stop_port_process() {
    local port=$1
    local name=$2
    
    print_message $BLUE "🔍 检查端口 $port ($name)..."
    
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -z "$pids" ]; then
        print_message $YELLOW "⚠️  端口 $port 未被占用"
        return 0
    fi
    
    print_message $BLUE "🛑 停止端口 $port 上的进程..."
    
    # 首先尝试优雅停止
    for pid in $pids; do
        if kill -TERM $pid 2>/dev/null; then
            print_message $GREEN "✅ 进程 $pid 已发送终止信号"
        fi
    done
    
    # 等待进程停止
    sleep 2
    
    # 检查是否还有进程运行
    local remaining_pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ ! -z "$remaining_pids" ]; then
        print_message $YELLOW "⚠️  强制停止剩余进程..."
        for pid in $remaining_pids; do
            if kill -KILL $pid 2>/dev/null; then
                print_message $GREEN "🔥 强制停止进程 $pid"
            fi
        done
    fi
    
    # 最终检查
    sleep 1
    if check_port_usage $port; then
        print_message $RED "❌ 端口 $port 仍被占用"
        return 1
    else
        print_message $GREEN "✅ 端口 $port 已释放"
        return 0
    fi
}

# 按进程名停止
stop_by_name() {
    local pattern=$1
    local name=$2
    
    print_message $BLUE "🔍 查找 $name 进程..."
    
    local pids=$(pgrep -f "$pattern")
    
    if [ -z "$pids" ]; then
        print_message $YELLOW "⚠️  未找到 $name 进程"
        return 0
    fi
    
    print_message $BLUE "🛑 停止 $name 进程..."
    
    # 优雅停止
    for pid in $pids; do
        if kill -TERM $pid 2>/dev/null; then
            print_message $GREEN "✅ $name 进程 $pid 已发送终止信号"
        fi
    done
    
    sleep 2
    
    # 检查剩余进程并强制停止
    local remaining_pids=$(pgrep -f "$pattern")
    if [ ! -z "$remaining_pids" ]; then
        print_message $YELLOW "⚠️  强制停止剩余 $name 进程..."
        for pid in $remaining_pids; do
            if kill -KILL $pid 2>/dev/null; then
                print_message $GREEN "🔥 强制停止 $name 进程 $pid"
            fi
        done
    fi
}

# 主函数
main() {
    print_banner
    
    print_message $BLUE "🔍 查找运行中的服务器进程..."
    echo
    
    # 按端口停止
    stop_port_process 5101 "本地历史服务器"
    stop_port_process 5100 "OpenAI兼容服务器"
    
    echo
    
    # 按进程名停止（额外保险）
    stop_by_name "local_history_server.py" "本地历史服务器"
    stop_by_name "openai_compatible_server.py" "OpenAI兼容服务器"
    
    echo
    print_message $BLUE "🔍 最终端口状态检查..."
    
    # 最终状态检查
    if check_port_usage 5101; then
        print_message $RED "❌ 端口 5101 仍被占用"
    else
        print_message $GREEN "✅ 端口 5101 已释放"
    fi
    
    if check_port_usage 5100; then
        print_message $RED "❌ 端口 5100 仍被占用"
    else
        print_message $GREEN "✅ 端口 5100 已释放"
    fi
    
    echo
    echo "========================================"
    print_message $GREEN "   ✅ 服务器停止操作完成"
    echo "========================================"
    echo
    
    if check_port_usage 5100 || check_port_usage 5101; then
        print_message $YELLOW "如果端口仍被占用，可能需要重启系统或手动处理"
        echo
        print_message $BLUE "可以使用以下命令查看端口占用："
        echo "  lsof -i:5100"
        echo "  lsof -i:5101"
    fi
}

# 检查必要命令
if ! command -v lsof &> /dev/null; then
    print_message $RED "❌ 错误: 未找到 lsof 命令"
    print_message $YELLOW "请安装 lsof: "
    print_message $YELLOW "  Ubuntu/Debian: sudo apt-get install lsof"
    print_message $YELLOW "  CentOS/RHEL: sudo yum install lsof"
    print_message $YELLOW "  macOS: brew install lsof (通常已预装)"
    exit 1
fi

# 运行主函数
main "$@"
