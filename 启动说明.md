# 🚀 AI Studio Bridge 一键启动说明

## 📋 启动脚本说明

本项目提供了多个启动脚本，确保服务器按正确顺序启动并提供监控功能。

### 🎯 主要启动脚本

#### 1. Windows 批处理脚本（推荐Windows用户）
- **`start_servers.bat`** - 主启动脚本
  - 自动检查Python环境和依赖
  - 按正确顺序启动两个服务器
  - 提供启动状态检查
  - 启动后自动进入监控模式

#### 2. Python 跨平台脚本（推荐Linux/macOS用户）
- **`start_servers.py`** - 跨平台启动脚本
  - 支持Windows、Linux、macOS
  - 更强大的错误处理和状态监控
  - 支持Ctrl+C优雅停止

### 🛠️ 辅助脚本

#### 监控脚本
- **`monitor_servers.bat`** - 服务器状态监控
  - 实时显示服务器运行状态
  - 提供停止、重启等控制选项
  - 显示服务器日志信息

#### 停止脚本
- **`stop_servers.bat`** - 停止所有服务器
  - 安全停止所有相关进程
  - 检查端口释放状态
  - 彻底清理残留进程

## 🚀 快速开始

### Windows 用户
1. 双击运行 `start_servers.bat`
2. 等待自动安装依赖和启动服务器
3. 按提示安装浏览器脚本

### Linux/macOS 用户
```bash
python3 start_servers.py
```

## 📊 服务器信息

启动成功后，将运行以下服务：

| 服务器 | 端口 | 地址 | 功能 |
|--------|------|------|------|
| 本地历史服务器 | 5101 | http://127.0.0.1:5101 | 处理历史记录和流式数据 |
| OpenAI兼容服务器 | 5100 | http://127.0.0.1:5100 | OpenAI API代理服务 |

## 🔧 启动顺序说明

脚本确保按以下顺序启动，避免依赖问题：

1. **环境检查** - 检查Python版本和必要文件
2. **依赖安装** - 自动安装requirements.txt中的依赖
3. **启动本地历史服务器** - 端口5101，提供基础服务
4. **等待确认** - 确保本地历史服务器完全启动
5. **启动OpenAI兼容服务器** - 端口5100，依赖本地历史服务器
6. **状态验证** - 确认两个服务器都正常运行

## ⚠️ 注意事项

### 端口占用
如果启动失败，可能是端口被占用：
- 运行 `stop_servers.bat` 停止所有相关进程
- 或手动检查端口：`netstat -an | findstr ":5100\|:5101"`

### 防火墙设置
确保防火墙允许本地端口5100和5101的访问

### 浏览器脚本
服务器启动后，还需要：
1. 安装Tampermonkey浏览器扩展
2. 安装TampermonkeyScript文件夹中的三个脚本
3. 打开AI Studio Chat历史对话页面

## 🐛 故障排除

### 常见问题

1. **Python版本过低**
   - 需要Python 3.8或更高版本
   - 运行 `python --version` 检查版本

2. **依赖安装失败**
   - 检查网络连接
   - 手动运行：`pip install -r requirements.txt`

3. **端口被占用**
   - 运行 `stop_servers.bat` 清理进程
   - 重启计算机释放端口

4. **服务器无响应**
   - 检查防火墙设置
   - 查看控制台错误信息

### 日志查看
- Windows: 启动脚本会显示实时状态
- 手动检查: 直接运行Python脚本查看详细日志

## 📞 技术支持

如果遇到问题：
1. 查看控制台输出的错误信息
2. 检查端口占用情况
3. 确认Python环境和依赖完整性
4. 参考项目README.md获取更多信息
