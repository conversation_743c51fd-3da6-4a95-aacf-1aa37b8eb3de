@echo off
chcp 65001 >nul
title AI Studio Bridge 服务器停止器

echo ========================================
echo   🛑 AI Studio Bridge 服务器停止器
echo ========================================
echo.

echo 🔍 查找运行中的服务器进程...

:: 查找并停止本地历史服务器
echo 🛑 停止本地历史服务器...
taskkill /f /fi "WindowTitle eq 本地历史服务器*" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  本地历史服务器未运行或已停止
) else (
    echo ✅ 本地历史服务器已停止
)

:: 查找并停止OpenAI兼容服务器
echo 🛑 停止OpenAI兼容服务器...
taskkill /f /fi "WindowTitle eq OpenAI兼容服务器*" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  OpenAI兼容服务器未运行或已停止
) else (
    echo ✅ OpenAI兼容服务器已停止
)

:: 查找并停止可能的Python进程（更彻底的清理）
echo 🔍 查找相关Python进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr "local_history_server\|openai_compatible_server"') do (
    echo 🛑 停止进程 %%i
    taskkill /f /pid %%i >nul 2>&1
)

:: 检查端口是否已释放
echo.
echo 🔍 检查端口状态...
netstat -an | findstr ":5101" >nul 2>&1
if errorlevel 1 (
    echo ✅ 端口 5101 已释放
) else (
    echo ⚠️  端口 5101 仍被占用
)

netstat -an | findstr ":5100" >nul 2>&1
if errorlevel 1 (
    echo ✅ 端口 5100 已释放
) else (
    echo ⚠️  端口 5100 仍被占用
)

echo.
echo ========================================
echo   ✅ 服务器停止操作完成
echo ========================================
echo.
echo 如果端口仍被占用，可能需要重启计算机或手动结束相关进程
echo.
pause
