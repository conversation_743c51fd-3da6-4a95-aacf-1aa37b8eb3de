@echo off
chcp 65001 >nul
title AI Studio Bridge 一键启动器

echo ========================================
echo   🚀 AI Studio Bridge 一键启动器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

:: 检查必要文件是否存在
if not exist "local_history_server.py" (
    echo ❌ 错误: 未找到 local_history_server.py
    pause
    exit /b 1
)

if not exist "openai_compatible_server.py" (
    echo ❌ 错误: 未找到 openai_compatible_server.py
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ 错误: 未找到 requirements.txt
    pause
    exit /b 1
)

:: 检查并安装依赖
echo 📦 检查Python依赖...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ 依赖安装失败，请检查网络连接或手动运行: pip install -r requirements.txt
    pause
    exit /b 1
)
echo ✅ 依赖检查完成

echo.
echo 🔧 正在启动服务器...
echo.

:: 启动本地历史服务器 (后台运行)
echo 🚀 启动本地历史服务器 (端口 5101)...
start "本地历史服务器" /min python local_history_server.py

:: 等待服务器启动
echo ⏳ 等待本地历史服务器启动...
timeout /t 3 /nobreak >nul

:: 检查本地历史服务器是否启动成功
powershell -Command "try { Invoke-WebRequest -Uri 'http://127.0.0.1:5101' -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ❌ 本地历史服务器启动失败，请检查端口5101是否被占用
    pause
    exit /b 1
)
echo ✅ 本地历史服务器启动成功

:: 启动OpenAI兼容服务器 (后台运行)
echo 🚀 启动OpenAI兼容服务器 (端口 5100)...
start "OpenAI兼容服务器" /min python openai_compatible_server.py

:: 等待服务器启动
echo ⏳ 等待OpenAI兼容服务器启动...
timeout /t 5 /nobreak >nul

:: 检查OpenAI兼容服务器是否启动成功
powershell -Command "try { Invoke-WebRequest -Uri 'http://127.0.0.1:5100' -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    echo ❌ OpenAI兼容服务器启动失败，请检查端口5100是否被占用
    pause
    exit /b 1
)
echo ✅ OpenAI兼容服务器启动成功

echo.
echo ========================================
echo   🎉 所有服务器启动成功！
echo ========================================
echo.
echo 📍 服务器地址:
echo   • 本地历史服务器: http://127.0.0.1:5101
echo   • OpenAI兼容服务器: http://127.0.0.1:5100
echo.
echo 📝 下一步操作:
echo   1. 确保浏览器已安装Tampermonkey扩展
echo   2. 安装TampermonkeyScript文件夹中的三个脚本:
echo      - automator.js
echo      - historyforger.js  
echo      - modelfetcher.js
echo   3. 打开AI Studio Chat历史对话页面(确保不是空对话页面)
echo.
echo ⚠️  注意: 关闭此窗口将停止所有服务器
echo 按任意键打开服务器状态监控...
pause >nul

:: 打开服务器状态监控
call monitor_servers.bat
