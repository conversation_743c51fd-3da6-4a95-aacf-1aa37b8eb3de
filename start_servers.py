#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Studio Bridge 一键启动脚本
支持Windows、Linux、macOS
"""

import os
import sys
import time
import subprocess
import threading
import requests
import signal
from pathlib import Path

class ServerManager:
    def __init__(self):
        self.processes = []
        self.running = True
        
    def print_banner(self):
        print("=" * 50)
        print("   🚀 AI Studio Bridge 一键启动器")
        print("=" * 50)
        print()
        
    def check_python_version(self):
        """检查Python版本"""
        if sys.version_info < (3, 8):
            print("❌ 错误: 需要Python 3.8或更高版本")
            print(f"   当前版本: {sys.version}")
            return False
        print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
        return True
        
    def check_files(self):
        """检查必要文件是否存在"""
        required_files = [
            "local_history_server.py",
            "openai_compatible_server.py", 
            "requirements.txt"
        ]
        
        for file in required_files:
            if not Path(file).exists():
                print(f"❌ 错误: 未找到 {file}")
                return False
                
        print("✅ 必要文件检查通过")
        return True
        
    def install_dependencies(self):
        """安装Python依赖"""
        print("📦 检查并安装Python依赖...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                print("❌ 依赖安装失败:")
                print(result.stderr)
                return False
                
            print("✅ 依赖安装完成")
            return True
            
        except subprocess.TimeoutExpired:
            print("❌ 依赖安装超时")
            return False
        except Exception as e:
            print(f"❌ 依赖安装出错: {e}")
            return False
            
    def check_port(self, port, timeout=5):
        """检查端口是否可用"""
        try:
            response = requests.get(f"http://127.0.0.1:{port}", timeout=timeout)
            return True
        except:
            return False
            
    def start_server(self, script_name, port, server_name):
        """启动服务器"""
        print(f"🚀 启动{server_name} (端口 {port})...")
        
        try:
            # 使用subprocess.Popen启动服务器
            process = subprocess.Popen([
                sys.executable, script_name
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes.append({
                'process': process,
                'name': server_name,
                'port': port,
                'script': script_name
            })
            
            # 等待服务器启动
            print(f"⏳ 等待{server_name}启动...")
            for i in range(10):  # 最多等待10秒
                time.sleep(1)
                if self.check_port(port):
                    print(f"✅ {server_name}启动成功")
                    return True
                    
            print(f"❌ {server_name}启动失败或超时")
            return False
            
        except Exception as e:
            print(f"❌ 启动{server_name}时出错: {e}")
            return False
            
    def monitor_servers(self):
        """监控服务器状态"""
        while self.running:
            time.sleep(5)  # 每5秒检查一次
            for server in self.processes:
                if server['process'].poll() is not None:
                    print(f"⚠️  {server['name']} 意外停止")
                    
    def stop_all_servers(self):
        """停止所有服务器"""
        print("\n🛑 正在停止所有服务器...")
        self.running = False
        
        for server in self.processes:
            try:
                server['process'].terminate()
                server['process'].wait(timeout=5)
                print(f"✅ {server['name']} 已停止")
            except subprocess.TimeoutExpired:
                server['process'].kill()
                print(f"🔥 强制停止 {server['name']}")
            except Exception as e:
                print(f"❌ 停止 {server['name']} 时出错: {e}")
                
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n\n收到中断信号，正在停止服务器...")
        self.stop_all_servers()
        sys.exit(0)
        
    def show_status(self):
        """显示服务器状态"""
        print("\n" + "=" * 50)
        print("   🎉 所有服务器启动成功！")
        print("=" * 50)
        print("\n📍 服务器地址:")
        print("   • 本地历史服务器: http://127.0.0.1:5101")
        print("   • OpenAI兼容服务器: http://127.0.0.1:5100")
        print("\n📝 下一步操作:")
        print("   1. 确保浏览器已安装Tampermonkey扩展")
        print("   2. 安装TampermonkeyScript文件夹中的三个脚本:")
        print("      - automator.js")
        print("      - historyforger.js")
        print("      - modelfetcher.js")
        print("   3. 打开AI Studio Chat历史对话页面(确保不是空对话页面)")
        print("\n⚠️  注意: 按 Ctrl+C 停止所有服务器")
        print("=" * 50)
        
    def run(self):
        """主运行函数"""
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.print_banner()
        
        # 检查环境
        if not self.check_python_version():
            return False
            
        if not self.check_files():
            return False
            
        if not self.install_dependencies():
            return False
            
        print("\n🔧 正在启动服务器...\n")
        
        # 启动本地历史服务器
        if not self.start_server("local_history_server.py", 5101, "本地历史服务器"):
            return False
            
        # 启动OpenAI兼容服务器
        if not self.start_server("openai_compatible_server.py", 5100, "OpenAI兼容服务器"):
            return False
            
        # 显示状态
        self.show_status()
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_servers, daemon=True)
        monitor_thread.start()
        
        # 保持主线程运行
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.signal_handler(signal.SIGINT, None)
            
        return True

if __name__ == "__main__":
    manager = ServerManager()
    success = manager.run()
    sys.exit(0 if success else 1)
