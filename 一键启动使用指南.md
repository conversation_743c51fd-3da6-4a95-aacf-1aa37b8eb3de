# 🚀 AI Studio Bridge 一键启动使用指南

## 📁 启动脚本文件说明

我已经为您创建了完整的一键启动脚本集合，确保服务器按正确顺序启动并提供完善的监控功能。

### 📋 文件清单

#### Windows 用户
- **`start_servers.bat`** - 主启动脚本（推荐）
- **`monitor_servers.bat`** - 服务器监控脚本
- **`stop_servers.bat`** - 停止所有服务器

#### Linux/macOS 用户  
- **`start_servers.sh`** - Shell启动脚本
- **`stop_servers.sh`** - Shell停止脚本

#### 跨平台
- **`start_servers.py`** - Python启动脚本（所有平台）

#### 说明文档
- **`启动说明.md`** - 详细技术说明
- **`一键启动使用指南.md`** - 本文件

## 🎯 快速开始

### Windows 用户（推荐方式）

1. **双击运行启动脚本**
   ```
   双击 start_servers.bat
   ```

2. **脚本会自动执行以下操作：**
   - ✅ 检查Python环境（需要3.8+）
   - ✅ 检查必要文件是否存在
   - ✅ 自动安装Python依赖
   - ✅ 按正确顺序启动两个服务器
   - ✅ 验证服务器启动状态
   - ✅ 进入监控模式

3. **启动成功后会显示：**
   ```
   ========================================
     🎉 所有服务器启动成功！
   ========================================
   
   📍 服务器地址:
     • 本地历史服务器: http://127.0.0.1:5101
     • OpenAI兼容服务器: http://127.0.0.1:5100
   ```

### Linux/macOS 用户

1. **给脚本添加执行权限**
   ```bash
   chmod +x start_servers.sh
   ```

2. **运行启动脚本**
   ```bash
   ./start_servers.sh
   ```

### 跨平台Python方式

```bash
python3 start_servers.py
```

## 🛠️ 服务器启动顺序

脚本确保按以下关键顺序启动，避免依赖问题：

1. **环境检查阶段**
   - 检查Python版本（≥3.8）
   - 验证必要文件存在
   - 自动安装requirements.txt依赖

2. **服务器启动阶段**
   - 🥇 **优先启动**: `local_history_server.py` (端口5101)
   - ⏳ **等待确认**: 确保本地历史服务器完全启动
   - 🥈 **然后启动**: `openai_compatible_server.py` (端口5100)
   - ✅ **状态验证**: 确认两个服务器都正常响应

3. **监控阶段**
   - 实时监控服务器状态
   - 提供控制选项（重启、停止、查看日志）

## 📊 服务器信息

| 服务器名称 | 端口 | 地址 | 功能描述 | 启动优先级 |
|------------|------|------|----------|------------|
| 本地历史服务器 | 5101 | http://127.0.0.1:5101 | 处理历史记录和流式数据 | 🥇 优先启动 |
| OpenAI兼容服务器 | 5100 | http://127.0.0.1:5100 | OpenAI API代理服务 | 🥈 依赖前者 |

## 🎛️ 监控和控制

### Windows 监控界面
启动后会自动进入监控模式，提供以下选项：
- **[R]** 刷新服务器状态
- **[S]** 停止所有服务器  
- **[L]** 查看服务器日志
- **[Q]** 退出监控（服务器继续运行）

### 手动停止服务器
```bash
# Windows
双击 stop_servers.bat

# Linux/macOS  
./stop_servers.sh
```

## ⚠️ 重要注意事项

### 🔒 启动顺序限制
- **必须先启动** `local_history_server.py` (5101端口)
- **然后才能启动** `openai_compatible_server.py` (5100端口)
- 脚本已自动处理此顺序，**请勿手动单独启动**

### 🚪 端口占用检查
如果启动失败，可能原因：
- 端口5100或5101被其他程序占用
- 运行 `stop_servers.bat` 清理残留进程
- 或重启计算机释放端口

### 🔥 防火墙设置
确保防火墙允许以下本地端口访问：
- 端口 5100 (OpenAI兼容服务器)
- 端口 5101 (本地历史服务器)

## 📝 完整使用流程

### 第一步：启动服务器
```bash
# Windows用户
双击 start_servers.bat

# Linux/macOS用户  
./start_servers.sh
```

### 第二步：安装浏览器脚本
1. 确保浏览器已安装 **Tampermonkey** 扩展
2. 依次安装 `TampermonkeyScript/` 文件夹中的脚本：
   - `automator.js` - 自动化脚本
   - `historyforger.js` - 历史记录处理
   - `modelfetcher.js` - 模型列表获取

### 第三步：使用服务
1. 打开 **AI Studio Chat历史对话页面**
2. **确保不是空对话页面**（这很重要！）
3. 脚本会自动与本地服务器交互

## 🐛 故障排除

### 常见问题及解决方案

1. **"Python版本过低"**
   - 需要Python 3.8或更高版本
   - 升级Python或使用虚拟环境

2. **"端口被占用"**
   - 运行对应的stop脚本清理进程
   - 检查其他程序是否占用5100/5101端口

3. **"依赖安装失败"**
   - 检查网络连接
   - 手动运行：`pip install -r requirements.txt`

4. **"服务器启动超时"**
   - 检查防火墙设置
   - 查看控制台错误信息
   - 确认Python环境完整

### 调试方法
1. 查看启动脚本的控制台输出
2. 手动运行Python脚本查看详细错误
3. 检查端口占用：`netstat -an | findstr ":5100\|:5101"`

## ✨ 脚本特色功能

### 🛡️ 安全保护
- 自动检查环境和依赖
- 优雅的进程停止机制
- 端口占用检测和清理

### 🚀 智能启动
- 按依赖关系正确排序
- 启动状态实时验证
- 失败时自动回滚清理

### 📊 实时监控
- 服务器状态持续监控
- 异常情况及时提醒
- 便捷的控制界面

### 🔧 跨平台支持
- Windows批处理脚本
- Linux/macOS Shell脚本  
- Python跨平台脚本

---

**🎉 现在您可以使用一键启动脚本轻松管理AI Studio Bridge服务器了！**

如有问题，请查看 `启动说明.md` 获取更多技术细节。
