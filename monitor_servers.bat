@echo off
chcp 65001 >nul
title AI Studio Bridge 服务器监控

:monitor_loop
cls
echo ========================================
echo   📊 AI Studio Bridge 服务器监控
echo ========================================
echo.
echo 🕐 当前时间: %date% %time%
echo.

:: 检查本地历史服务器状态
echo 🔍 检查本地历史服务器 (端口 5101)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:5101' -TimeoutSec 3; Write-Host '✅ 本地历史服务器: 运行正常' -ForegroundColor Green } catch { Write-Host '❌ 本地历史服务器: 无响应或错误' -ForegroundColor Red }" 2>nul

:: 检查OpenAI兼容服务器状态  
echo 🔍 检查OpenAI兼容服务器 (端口 5100)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://127.0.0.1:5100' -TimeoutSec 3; Write-Host '✅ OpenAI兼容服务器: 运行正常' -ForegroundColor Green } catch { Write-Host '❌ OpenAI兼容服务器: 无响应或错误' -ForegroundColor Red }" 2>nul

echo.
echo ========================================
echo   🎛️  控制选项
echo ========================================
echo.
echo [R] 刷新状态
echo [S] 停止所有服务器
echo [L] 查看服务器日志
echo [Q] 退出监控
echo.
set /p choice="请选择操作 (R/S/L/Q): "

if /i "%choice%"=="R" goto monitor_loop
if /i "%choice%"=="S" goto stop_servers
if /i "%choice%"=="L" goto show_logs
if /i "%choice%"=="Q" goto exit_monitor

goto monitor_loop

:stop_servers
echo.
echo 🛑 正在停止所有服务器...
taskkill /f /fi "WindowTitle eq 本地历史服务器*" >nul 2>&1
taskkill /f /fi "WindowTitle eq OpenAI兼容服务器*" >nul 2>&1
echo ✅ 所有服务器已停止
timeout /t 2 /nobreak >nul
goto exit_monitor

:show_logs
echo.
echo 📋 服务器进程信息:
echo.
tasklist /fi "imagename eq python.exe" /fo table 2>nul | findstr python
echo.
echo 按任意键返回监控界面...
pause >nul
goto monitor_loop

:exit_monitor
echo.
echo 👋 退出监控，服务器继续在后台运行
echo 如需停止服务器，请重新运行此脚本并选择停止选项
timeout /t 2 /nobreak >nul
exit /b 0
